//@version=5
//
strategy(title='MR WOW PERSONAL - Enhanced Lower TF', overlay=true, pyramiding=0, default_qty_type=strategy.percent_of_equity, default_qty_value=10, calc_on_every_tick=false)
//
// === INPUTS ===
res    = input.timeframe(title='TIMEFRAME', defval='15', group ="NON REPAINT")
lowerTfEnabled = input.bool(defval=true, title="Enable Lower Timeframes (Seconds)", group="NON REPAINT")
lowerTfValue = input.string(defval="10", title="Lower Timeframe (Seconds)", options=["1", "5", "10", "15", "30"], group="NON REPAINT")

// Enhanced Lower Timeframe Settings
showLowerTfIndicators = input.bool(defval=true, title="Show Lower TF Indicators", group="LOWER TF INDICATORS")
showLowerTfRSI = input.bool(defval=true, title="Show Lower TF RSI Signals", group="LOWER TF INDICATORS")
showLowerTfMACD = input.bool(defval=true, title="Show Lower TF MACD Signals", group="LOWER TF INDICATORS")
showLowerTfBB = input.bool(defval=true, title="Show Lower TF Bollinger Bands", group="LOWER TF INDICATORS")
showLowerTfSupertrend = input.bool(defval=true, title="Show Lower TF Supertrend", group="LOWER TF INDICATORS")
showLowerTfVolume = input.bool(defval=true, title="Show Lower TF Volume Alerts", group="LOWER TF INDICATORS")

// Lower TF Alert Settings
lowerTfAlertSensitivity = input.string(defval="Medium", title="Lower TF Alert Sensitivity", options=["Low", "Medium", "High"], group="LOWER TF ALERTS")
showLowerTfDivergence = input.bool(defval=true, title="Show Lower TF Divergence", group="LOWER TF ALERTS")
showLowerTfBreakouts = input.bool(defval=true, title="Show Lower TF Breakouts", group="LOWER TF ALERTS")

useRes = input(defval=true, title='Use Alternate Signals')
intRes = input(defval=8, title='Multiplier for Alernate Signals')
stratRes = timeframe.ismonthly ? str.tostring(timeframe.multiplier * intRes, '###M') :
       timeframe.isweekly ? str.tostring(timeframe.multiplier * intRes, '###W') :
       timeframe.isdaily ? str.tostring(timeframe.multiplier * intRes, '###D') :
       timeframe.isintraday and not timeframe.isseconds ? str.tostring(timeframe.multiplier * intRes, '####') :
       timeframe.isseconds ? str.tostring(timeframe.multiplier * intRes) + "S" : '60'
basisType = input.string(defval='ALMA', title='MA Type: ', options=['TEMA', 'HullMA', 'ALMA'])
basisLen = input.int(defval=2, title='MA Period', minval=1)
offsetSigma = input.int(defval=5, title='Offset for LSMA / Sigma for ALMA', minval=0)
offsetALMA = input.float(defval=0.85, title='Offset for ALMA', minval=0, step=0.01)
scolor = input(true, title='Show coloured Bars to indicate Trend?')
delayOffset = input.int(defval=0, title='Delay Open/Close MA (Forces Non-Repainting)', minval=0, step=1)
tradeType = input.string('BOTH', title='What trades should be taken : ', options=['LONG', 'SHORT', 'BOTH', 'NONE'])
// === /INPUTS ===
h = input(false, title='Signals for Heikin Ashi Candles')
src = h ? request.security(ticker.heikinashi(syminfo.tickerid), timeframe.period, close, lookahead=barmerge.lookahead_off) : close
//      INDICATOR SETTINGS
swing_length = input.int(10, title = 'Swing High/Low Length', group = 'Settings', minval = 1, maxval = 50)
history_of_demand_to_keep = input.int(20, title = 'History To Keep', minval = 5, maxval = 50)
box_width = input.float(2.5, title = 'Supply/Demand Box Width', group = 'Settings', minval = 1, maxval = 10, step = 0.5)

//      INDICATOR VISUAL SETTINGS
show_zigzag = input.bool(false, title = 'Show Zig Zag', group = 'Visual Settings', inline = '1')
show_price_action_labels = input.bool(false, title = 'Show Price Action Labels', group = 'Visual Settings', inline = '2')

supply_color = input.color(color.new(#EDEDED,70), title = 'Supply', group = 'Visual Settings', inline = '3')
supply_outline_color = input.color(color.new(color.white,75), title = 'Outline', group = 'Visual Settings', inline = '3')

demand_color = input.color(color.new(#00FFFF,70), title = 'Demand', group = 'Visual Settings', inline = '4')
demand_outline_color = input.color(color.new(color.white,75), title = 'Outline', group = 'Visual Settings', inline = '4')

bos_label_color = input.color(color.white, title = 'BOS Label', group = 'Visual Settings', inline = '5')
poi_label_color = input.color(color.white, title = 'POI Label', group = 'Visual Settings', inline = '7')

swing_type_color = input.color(color.black, title = 'Price Action Label', group = 'Visual Settings', inline = '8')
zigzag_color = input.color(color.new(#000000,0), title = 'Zig Zag', group = 'Visual Settings', inline = '9')

//
//END SETTINGS
//


//
//FUNCTIONS
//

//      FUNCTION TO ADD NEW AND REMOVE LAST IN ARRAY
f_array_add_pop(array, new_value_to_add) =>
    array.unshift(array, new_value_to_add)
    array.pop(array)

//      FUNCTION SWING H & L LABELS
f_sh_sl_labels(array, swing_type) =>

    var string label_text = na
    if swing_type == 1
        if array.get(array, 0) >= array.get(array, 1)
            label_text := 'HH'
        else
            label_text := 'LH'
        label.new(bar_index - swing_length, array.get(array,0), text = label_text, style=label.style_label_down, textcolor = swing_type_color, color = color.new(swing_type_color, 100), size = size.tiny)

    else if swing_type == -1
        if array.get(array, 0) >= array.get(array, 1)
            label_text := 'HL'
        else
            label_text := 'LL'
        label.new(bar_index - swing_length, array.get(array,0), text = label_text, style=label.style_label_up, textcolor = swing_type_color, color = color.new(swing_type_color, 100), size = size.tiny)

//      FUNCTION MAKE SURE SUPPLY ISNT OVERLAPPING
f_check_overlapping(new_poi, box_array, atr) =>

    atr_threshold = atr * 2
    okay_to_draw = true

    for i = 0 to array.size(box_array) - 1
        top = box.get_top(array.get(box_array, i))
        bottom = box.get_bottom(array.get(box_array, i))
        poi = (top + bottom) / 2

        upper_boundary = poi + atr_threshold
        lower_boundary = poi - atr_threshold

        if new_poi >= lower_boundary and new_poi <= upper_boundary
            okay_to_draw := false
            break
        else
            okay_to_draw := true
    okay_to_draw


//      FUNCTION TO DRAW SUPPLY OR DEMAND ZONE
f_supply_demand(value_array, bn_array, box_array, label_array, box_type, atr) =>

    atr_buffer = atr * (box_width / 10)
    box_left = array.get(bn_array, 0)
    box_right = bar_index

    var float box_top = 0.00
    var float box_bottom = 0.00
    var float poi = 0.00


    if box_type == 1
        box_top := array.get(value_array, 0)
        box_bottom := box_top - atr_buffer
        poi := (box_top + box_bottom) / 2
    else if box_type == -1
        box_bottom := array.get(value_array, 0)
        box_top := box_bottom + atr_buffer
        poi := (box_top + box_bottom) / 2

    okay_to_draw = f_check_overlapping(poi, box_array, atr)
    // okay_to_draw = true

    //delete oldest box, and then create a new box and add it to the array
    if box_type == 1 and okay_to_draw
        box.delete( array.get(box_array, array.size(box_array) - 1) )
        f_array_add_pop(box_array, box.new( left = box_left, top = box_top, right = box_right, bottom = box_bottom, border_color = supply_outline_color,
             bgcolor = supply_color, extend = extend.right, text = 'SUPPLY', text_halign = text.align_center, text_valign = text.align_center, text_color = poi_label_color, text_size = size.small, xloc = xloc.bar_index))

        box.delete( array.get(label_array, array.size(label_array) - 1) )
        f_array_add_pop(label_array, box.new( left = box_left, top = poi, right = box_right, bottom = poi, border_color = color.new(poi_label_color,90),
             bgcolor = color.new(poi_label_color,90), extend = extend.right, text = 'POI', text_halign = text.align_left, text_valign = text.align_center, text_color = poi_label_color, text_size = size.small, xloc = xloc.bar_index))

    else if box_type == -1 and okay_to_draw
        box.delete( array.get(box_array, array.size(box_array) - 1) )
        f_array_add_pop(box_array, box.new( left = box_left, top = box_top, right = box_right, bottom = box_bottom, border_color = demand_outline_color,
             bgcolor = demand_color, extend = extend.right,  text = 'DEMAND', text_halign = text.align_center, text_valign = text.align_center, text_color = poi_label_color, text_size = size.small, xloc = xloc.bar_index))

        box.delete( array.get(label_array, array.size(label_array) - 1) )
        f_array_add_pop(label_array, box.new( left = box_left, top = poi, right = box_right, bottom = poi, border_color = color.new(poi_label_color,90),
             bgcolor = color.new(poi_label_color,90), extend = extend.right,  text = 'POI', text_halign = text.align_left, text_valign = text.align_center, text_color = poi_label_color, text_size = size.small, xloc = xloc.bar_index))


//      FUNCTION TO CHANGE SUPPLY/DEMAND TO A BOS IF BROKEN
f_sd_to_bos(box_array, bos_array, label_array, zone_type) =>

    if zone_type == 1
        for i = 0 to array.size(box_array) - 1
            level_to_break = box.get_top(array.get(box_array,i))
            // if ta.crossover(close, level_to_break)
            if close >= level_to_break
                copied_box = box.copy(array.get(box_array,i))
                f_array_add_pop(bos_array, copied_box)
                mid = (box.get_top(array.get(box_array,i)) + box.get_bottom(array.get(box_array,i))) / 2
                box.set_top(array.get(bos_array,0), mid)
                box.set_bottom(array.get(bos_array,0), mid)
                box.set_extend( array.get(bos_array,0), extend.none)
                box.set_right( array.get(bos_array,0), bar_index)
                box.set_text( array.get(bos_array,0), 'BOS' )
                box.set_text_color( array.get(bos_array,0), bos_label_color)
                box.set_text_size( array.get(bos_array,0), size.small)
                box.set_text_halign( array.get(bos_array,0), text.align_center)
                box.set_text_valign( array.get(bos_array,0), text.align_center)
                box.delete(array.get(box_array, i))
                box.delete(array.get(label_array, i))


    if zone_type == -1
        for i = 0 to array.size(box_array) - 1
            level_to_break = box.get_bottom(array.get(box_array,i))
            // if ta.crossunder(close, level_to_break)
            if close <= level_to_break
                copied_box = box.copy(array.get(box_array,i))
                f_array_add_pop(bos_array, copied_box)
                mid = (box.get_top(array.get(box_array,i)) + box.get_bottom(array.get(box_array,i))) / 2
                box.set_top(array.get(bos_array,0), mid)
                box.set_bottom(array.get(bos_array,0), mid)
                box.set_extend( array.get(bos_array,0), extend.none)
                box.set_right( array.get(bos_array,0), bar_index)
                box.set_text( array.get(bos_array,0), 'BOS' )
                box.set_text_color( array.get(bos_array,0), bos_label_color)
                box.set_text_size( array.get(bos_array,0), size.small)
                box.set_text_halign( array.get(bos_array,0), text.align_center)
                box.set_text_valign( array.get(bos_array,0), text.align_center)
                box.delete(array.get(box_array, i))
                box.delete(array.get(label_array, i))



//      FUNCTION MANAGE CURRENT BOXES BY CHANGING ENDPOINT
f_extend_box_endpoint(box_array) =>

    for i = 0 to array.size(box_array) - 1
        box.set_right(array.get(box_array, i), bar_index + 100)


//
//END FUNCTIONS
//

//
// === ENHANCED LOWER TIMEFRAME FUNCTIONS ===
//

// Function to get lower timeframe data with proper handling
f_getLowerTfData(src, tf) =>
    hasS = str.contains(tf, "S")
    actualTf = hasS ? tf : tf + "S"

    // Get lower timeframe data array
    ltf_data = request.security_lower_tf(syminfo.tickerid, actualTf, src)

    // Return the most recent value or current value if array is empty
    array.size(ltf_data) > 0 ? array.get(ltf_data, array.size(ltf_data) - 1) : src

// Function to calculate RSI on lower timeframe
f_lowerTfRSI(tf, length) =>
    ltf_close = f_getLowerTfData(close, tf)
    ta.rsi(ltf_close, length)

// Function to calculate MACD on lower timeframe
f_lowerTfMACD(tf, fastLength, slowLength, signalLength) =>
    ltf_close = f_getLowerTfData(close, tf)
    [macdLine, signalLine, histLine] = ta.macd(ltf_close, fastLength, slowLength, signalLength)
    [macdLine, signalLine, histLine]

// Function to calculate Bollinger Bands on lower timeframe
f_lowerTfBB(tf, length, mult) =>
    ltf_close = f_getLowerTfData(close, tf)
    [middle, upper, lower] = ta.bb(ltf_close, length, mult)
    [middle, upper, lower]

// Function to calculate Supertrend on lower timeframe
f_lowerTfSupertrend(tf, factor, atrLen) =>
    ltf_high = f_getLowerTfData(high, tf)
    ltf_low = f_getLowerTfData(low, tf)
    ltf_close = f_getLowerTfData(close, tf)
    ltf_hl2 = (ltf_high + ltf_low) / 2

    atr = ta.atr(atrLen)
    upperBand = ltf_hl2 + factor * atr
    lowerBand = ltf_hl2 - factor * atr

    prevLowerBand = nz(lowerBand[1])
    prevUpperBand = nz(upperBand[1])

    lowerBand := lowerBand > prevLowerBand or ltf_close[1] < prevLowerBand ? lowerBand : prevLowerBand
    upperBand := upperBand < prevUpperBand or ltf_close[1] > prevUpperBand ? upperBand : prevUpperBand

    int direction = na
    float superTrend = na
    prevSuperTrend = superTrend[1]

    if na(atr[1])
        direction := 1
    else if prevSuperTrend == prevUpperBand
        direction := ltf_close > upperBand ? -1 : 1
    else
        direction := ltf_close < lowerBand ? 1 : -1

    superTrend := direction == -1 ? lowerBand : upperBand
    [superTrend, direction]

// Function to detect volume spikes on lower timeframe
f_lowerTfVolumeSpike(tf, multiplier) =>
    ltf_volume = f_getLowerTfData(volume, tf)
    avgVolume = ta.sma(ltf_volume, 20)
    ltf_volume > avgVolume * multiplier

// Function to detect divergence on lower timeframe
f_lowerTfDivergence(tf, src, indicator) =>
    ltf_src = f_getLowerTfData(src, tf)
    ltf_indicator = f_getLowerTfData(indicator, tf)

    // Simple divergence detection
    srcHigh = ta.pivothigh(ltf_src, 5, 5)
    srcLow = ta.pivotlow(ltf_src, 5, 5)
    indHigh = ta.pivothigh(ltf_indicator, 5, 5)
    indLow = ta.pivotlow(ltf_indicator, 5, 5)

    bullDiv = not na(srcLow) and not na(indLow) and srcLow < srcLow[1] and indLow > indLow[1]
    bearDiv = not na(srcHigh) and not na(indHigh) and srcHigh > srcHigh[1] and indHigh < indHigh[1]

    [bullDiv, bearDiv]

// Function to detect breakouts on lower timeframe
f_lowerTfBreakout(tf, length) =>
    ltf_high = f_getLowerTfData(high, tf)
    ltf_low = f_getLowerTfData(low, tf)
    ltf_close = f_getLowerTfData(close, tf)

    highestHigh = ta.highest(ltf_high, length)
    lowestLow = ta.lowest(ltf_low, length)

    bullBreakout = ltf_close > highestHigh[1]
    bearBreakout = ltf_close < lowestLow[1]

    [bullBreakout, bearBreakout]

//
// === END ENHANCED LOWER TIMEFRAME FUNCTIONS ===
//

//
//CALCULATIONS
//

//      CALCULATE ATR
atr = ta.atr(50)

//      CALCULATE SWING HIGHS & SWING LOWS
swing_high = ta.pivothigh(high, swing_length, swing_length)
swing_low = ta.pivotlow(low, swing_length, swing_length)

//      ARRAYS FOR SWING H/L & BN
var swing_high_values = array.new_float(5,0.00)
var swing_low_values = array.new_float(5,0.00)

var swing_high_bns = array.new_int(5,0)
var swing_low_bns = array.new_int(5,0)

//      ARRAYS FOR SUPPLY / DEMAND
var current_supply_box = array.new_box(history_of_demand_to_keep, na)
var current_demand_box = array.new_box(history_of_demand_to_keep, na)

//      ARRAYS FOR SUPPLY / DEMAND POI LABELS
var current_supply_poi = array.new_box(history_of_demand_to_keep, na)
var current_demand_poi = array.new_box(history_of_demand_to_keep, na)

//      ARRAYS FOR BOS
var supply_bos = array.new_box(5, na)
var demand_bos = array.new_box(5, na)

//
// === ENHANCED LOWER TIMEFRAME CALCULATIONS ===
//

// Lower timeframe indicator calculations
lowerTfRSI = lowerTfEnabled and showLowerTfRSI ? f_lowerTfRSI(lowerTfValue, 14) : na
[lowerTfMACD, lowerTfMACDSignal, lowerTfMACDHist] = lowerTfEnabled and showLowerTfMACD ? f_lowerTfMACD(lowerTfValue, 12, 26, 9) : [na, na, na]
[lowerTfBBMiddle, lowerTfBBUpper, lowerTfBBLower] = lowerTfEnabled and showLowerTfBB ? f_lowerTfBB(lowerTfValue, 20, 2) : [na, na, na]
[lowerTfST, lowerTfSTDirection] = lowerTfEnabled and showLowerTfSupertrend ? f_lowerTfSupertrend(lowerTfValue, 3, 10) : [na, na]

// Lower timeframe alert conditions
lowerTfVolumeSpike = lowerTfEnabled and showLowerTfVolume ? f_lowerTfVolumeSpike(lowerTfValue, 2) : false
[lowerTfBullDiv, lowerTfBearDiv] = lowerTfEnabled and showLowerTfDivergence ? f_lowerTfDivergence(lowerTfValue, close, lowerTfRSI) : [false, false]
[lowerTfBullBreakout, lowerTfBearBreakout] = lowerTfEnabled and showLowerTfBreakouts ? f_lowerTfBreakout(lowerTfValue, 20) : [false, false]

// Enhanced alert sensitivity settings
sensitivityMultiplier = lowerTfAlertSensitivity == "High" ? 0.5 : lowerTfAlertSensitivity == "Medium" ? 1.0 : 1.5

// Lower timeframe RSI conditions
lowerTfRSIOverbought = lowerTfRSI > (70 * sensitivityMultiplier)
lowerTfRSIOversold = lowerTfRSI < (30 * sensitivityMultiplier)
lowerTfRSIBullCross = ta.crossover(lowerTfRSI, 50)
lowerTfRSIBearCross = ta.crossunder(lowerTfRSI, 50)

// Lower timeframe MACD conditions
lowerTfMACDBullCross = ta.crossover(lowerTfMACD, lowerTfMACDSignal)
lowerTfMACDBearCross = ta.crossunder(lowerTfMACD, lowerTfMACDSignal)
lowerTfMACDHistBull = lowerTfMACDHist > lowerTfMACDHist[1]
lowerTfMACDHistBear = lowerTfMACDHist < lowerTfMACDHist[1]

// Lower timeframe Bollinger Bands conditions
lowerTfBBBullSignal = close > lowerTfBBUpper
lowerTfBBBearSignal = close < lowerTfBBLower
lowerTfBBSqueeze = (lowerTfBBUpper - lowerTfBBLower) < ta.sma(lowerTfBBUpper - lowerTfBBLower, 20) * 0.8

// Lower timeframe Supertrend conditions
lowerTfSTBull = lowerTfSTDirection == -1
lowerTfSTBear = lowerTfSTDirection == 1
lowerTfSTBullChange = lowerTfSTDirection == -1 and lowerTfSTDirection[1] == 1
lowerTfSTBearChange = lowerTfSTDirection == 1 and lowerTfSTDirection[1] == -1

//
// === END ENHANCED LOWER TIMEFRAME CALCULATIONS ===
//

//
//END CALCULATIONS
//

//      NEW SWING HIGH
if not na(swing_high)

    //MANAGE SWING HIGH VALUES
    f_array_add_pop(swing_high_values, swing_high)
    f_array_add_pop(swing_high_bns, bar_index[swing_length])
    if show_price_action_labels
        f_sh_sl_labels(swing_high_values, 1)

    f_supply_demand(swing_high_values, swing_high_bns, current_supply_box, current_supply_poi, 1, atr)

//      NEW SWING LOW
else if not na(swing_low)

    //MANAGE SWING LOW VALUES
    f_array_add_pop(swing_low_values, swing_low)
    f_array_add_pop(swing_low_bns, bar_index[swing_length])
    if show_price_action_labels
        f_sh_sl_labels(swing_low_values, -1)

    f_supply_demand(swing_low_values, swing_low_bns, current_demand_box, current_demand_poi, -1, atr)


f_sd_to_bos(current_supply_box, supply_bos, current_supply_poi, 1)
f_sd_to_bos(current_demand_box, demand_bos, current_demand_poi, -1)

f_extend_box_endpoint(current_supply_box)
f_extend_box_endpoint(current_demand_box)

// if barstate.islast
    // label.new(x = bar_index + 10, y = close[1], text = str.tostring( array.size(current_supply_poi) ))
//     label.new(x = bar_index + 20, y = close[1], text = str.tostring( box.get_bottom( array.get(current_supply_box, 0))))
//     label.new(x = bar_index + 30, y = close[1], text = str.tostring( box.get_bottom( array.get(current_supply_box, 1))))
//     label.new(x = bar_index + 40, y = close[1], text = str.tostring( box.get_bottom( array.get(current_supply_box, 2))))
//     label.new(x = bar_index + 50, y = close[1], text = str.tostring( box.get_bottom( array.get(current_supply_box, 3))))
//     label.new(x = bar_index + 60, y = close[1], text = str.tostring( box.get_bottom( array.get(current_supply_box, 4))))

// Get user input

channelBal     = input.bool(false, "Channel Balance", group = "CHART")



// Functions
supertrend(_src, factor, atrLen) =>
	atr = ta.atr(atrLen)
	upperBand = _src + factor * atr
	lowerBand = _src - factor * atr
	prevLowerBand = nz(lowerBand[1])
	prevUpperBand = nz(upperBand[1])
	lowerBand := lowerBand > prevLowerBand or close[1] < prevLowerBand ? lowerBand : prevLowerBand
	upperBand := upperBand < prevUpperBand or close[1] > prevUpperBand ? upperBand : prevUpperBand
	int direction = na
	float superTrend = na
	prevSuperTrend = superTrend[1]
	if na(atr[1])
		direction := 1
	else if prevSuperTrend == prevUpperBand
		direction := close > upperBand ? -1 : 1
	else
		direction := close < lowerBand ? 1 : -1
	superTrend := direction == -1 ? lowerBand : upperBand
	[superTrend, direction]
lr_slope(_src, _len) =>
    x = 0.0, y = 0.0, x2 = 0.0, xy = 0.0
    for i = 0 to _len - 1
        val = _src[i]
        per = i + 1
        x += per
        y += val
        x2 += per * per
        xy += val * per
    _slp = (_len * xy - x * y) / (_len * x2 - x * x)
    _avg = y / _len
    _int = _avg - _slp * x / _len + _slp
    [_slp, _avg, _int]
lr_dev(_src, _len, _slp, _avg, _int) =>
    upDev = 0.0, dnDev = 0.0
    val = _int
    for j = 0 to _len - 1
        price = high[j] - val
        if price > upDev
            upDev := price
        price := val - low[j]
        if price > dnDev
            dnDev := price
        price := _src[j]
        val += _slp
    [upDev, dnDev]


// Get Components
ocAvg       = math.avg(open, close)
sma1        = ta.sma(close, 5)
sma2        = ta.sma(close, 6)
sma3        = ta.sma(close, 7)
sma4        = ta.sma(close, 8)
sma5        = ta.sma(close, 9)
sma6        = ta.sma(close, 10)
sma7        = ta.sma(close, 11)
sma8        = ta.sma(close, 12)
sma9        = ta.sma(close, 13)
sma10       = ta.sma(close, 14)
sma11       = ta.sma(close, 15)
sma12       = ta.sma(close, 16)
sma13       = ta.sma(close, 17)
sma14       = ta.sma(close, 18)
sma15       = ta.sma(close, 19)
sma16       = ta.sma(close, 20)
psar        = ta.sar(0.02, 0.02, 0.2)
[middleKC1, upperKC1, lowerKC1] = ta.kc(close, 80, 10.5)
[middleKC2, upperKC2, lowerKC2] = ta.kc(close, 80, 9.5)
[middleKC3, upperKC3, lowerKC3] = ta.kc(close, 80, 8)
[middleKC4, upperKC4, lowerKC4] = ta.kc(close, 80, 3)

barsL       = 10
barsR       = 10
pivotHigh = fixnan(ta.pivothigh(barsL, barsR)[1])
pivotLow = fixnan(ta.pivotlow(barsL, barsR)[1])
source = close, period = 150
[s, a, i] = lr_slope(source, period)
[upDev, dnDev] = lr_dev(source, period, s, a, i)

// Colors
green       = #00d9ff, green2   = #00d9ff
red         = #ff0090, red2     = #ff0090

// Plots
k1 = plot(ta.ema(upperKC1, 50), "", na, editable=false)
k2 = plot(ta.ema(upperKC2, 50), "", na, editable=false)
k3 = plot(ta.ema(upperKC3, 50), "", na, editable=false)
k4 = plot(ta.ema(upperKC4, 50), "", na, editable=false)
k5 = plot(ta.ema(lowerKC4, 50), "", na, editable=false)
k6 = plot(ta.ema(lowerKC3, 50), "", na, editable=false)
k7 = plot(ta.ema(lowerKC2, 50), "", na, editable=false)
k8 = plot(ta.ema(lowerKC1, 50), "", na, editable=false)
fill(k1, k2, channelBal ? color.new(red2, 40) : na, editable=false)
fill(k2, k3, channelBal ? color.new(red2, 65) : na, editable=false)
fill(k3, k4, channelBal ? color.new(red2, 90) : na, editable=false)
fill(k5, k6, channelBal ? color.new(green2, 90) : na, editable=false)
fill(k6, k7, channelBal ? color.new(green2, 65) : na, editable=false)
fill(k7, k8, channelBal ? color.new(green2, 40) : na, editable=false)



y1 = low - (ta.atr(30) * 2), y1B = low - ta.atr(30)
y2 = high + (ta.atr(30) * 2), y2B = high + ta.atr(30)



x1 = bar_index - period + 1, _y1 = i + s * (period - 1), x2 = bar_index, _y2 = i


//Functions
//Line Style function
get_line_style(style) =>
    out = switch style
        '???'  => line.style_solid
        '----' => line.style_dashed
        '····' => line.style_dotted

//Function to get order block coordinates
get_coordinates(condition, top, btm, ob_val)=>
    var ob_top  = array.new_float(0)
    var ob_btm  = array.new_float(0)
    var ob_avg  = array.new_float(0)
    var ob_left = array.new_int(0)

    float ob = na

    //Append coordinates to arrays
    if condition
        avg = math.avg(top, btm)

        array.unshift(ob_top, top)
        array.unshift(ob_btm, btm)
        array.unshift(ob_avg, avg)


        ob := ob_val

    [ob_top, ob_btm, ob_avg, ob_left, ob]

//Function to remove mitigated order blocks from coordinate arrays
remove_mitigated(ob_top, ob_btm, ob_left, ob_avg, target, bull)=>
    mitigated = false
    target_array = bull ? ob_btm : ob_top

    for element in target_array
        idx = array.indexof(target_array, element)

        if (bull ? target < element : target > element)
            mitigated := true

            array.remove(ob_top, idx)
            array.remove(ob_btm, idx)
            array.remove(ob_avg, idx)
            array.remove(ob_left, idx)

    mitigated

//Function to set order blocks
set_order_blocks(ob_top, ob_btm, ob_left, ob_avg, ext_last, bg_css, border_css, lvl_css)=>
    var ob_box = array.new_box(0)
    var ob_lvl = array.new_line(0)



//Global elements
var os = 0
var target_bull = 0.
var target_bear = 0.

// Constants colours that include fully non-transparent option.
green100 = #008000FF
lime100 = #00FF00FF
red100 = #FF0000FF
blue100 = #0000FFFF
aqua100 = #00FFFFFF
darkred100 = #8B0000FF
gray100 = #808080FF

/////////////////////////////////////////////
// Create non-repainting security function
rp_security(_symbol, _res, _src) =>
    request.security(_symbol, _res, _src[barstate.isrealtime ? 1 : 0])

htfHigh = rp_security(syminfo.tickerid, res, high)
htfLow = rp_security(syminfo.tickerid, res, low)

// Main Indicator
// Functions
smoothrng(x, t, m) =>
    wper = t * 2 - 1
    avrng = ta.ema(math.abs(x - x[1]), t)
    smoothrng = ta.ema(avrng, wper) * m
rngfilt(x, r) =>
    rngfilt = x
    rngfilt := x > nz(rngfilt[1]) ? x - r < nz(rngfilt[1]) ? nz(rngfilt[1]) : x - r : x + r > nz(rngfilt[1]) ? nz(rngfilt[1]) : x + r
percWidth(len, perc) => (ta.highest(len) - ta.lowest(len)) * perc / 100
securityNoRep(sym, res, src) => request.security(sym, res, src, barmerge.gaps_off, barmerge.lookahead_on)
swingPoints(prd) =>
    pivHi = ta.pivothigh(prd, prd)
    pivLo = ta.pivotlow (prd, prd)
    last_pivHi = ta.valuewhen(pivHi, pivHi, 1)
    last_pivLo = ta.valuewhen(pivLo, pivLo, 1)
    hh = pivHi and pivHi > last_pivHi ? pivHi : na
    lh = pivHi and pivHi < last_pivHi ? pivHi : na
    hl = pivLo and pivLo > last_pivLo ? pivLo : na
    ll = pivLo and pivLo < last_pivLo ? pivLo : na
    [hh, lh, hl, ll]
f_chartTfInMinutes() =>
    float _resInMinutes = timeframe.multiplier * (
      timeframe.isseconds ? 1.0/60.0            :
      timeframe.isminutes ? 1.                  :
      timeframe.isdaily   ? 60. * 24            :
      timeframe.isweekly  ? 60. * 24 * 7        :
      timeframe.ismonthly ? 60. * 24 * 30.4375  : na)
f_kc(src, len, sensitivity) =>
    basis = ta.sma(src, len)
    span  = ta.atr(len)
    [basis + span * sensitivity, basis - span * sensitivity]
wavetrend(src, chlLen, avgLen) =>
    esa = ta.ema(src, chlLen)
    d = ta.ema(math.abs(src - esa), chlLen)
    ci = (src - esa) / (0.015 * d)
    wt1 = ta.ema(ci, avgLen)
    wt2 = ta.sma(wt1, 3)
    [wt1, wt2]
f_top_fractal(src) => src[4] < src[2] and src[3] < src[2] and src[2] > src[1] and src[2] > src[0]
f_bot_fractal(src) => src[4] > src[2] and src[3] > src[2] and src[2] < src[1] and src[2] < src[0]
f_fractalize (src) => f_top_fractal(src) ? 1 : f_bot_fractal(src) ? -1 : 0
f_findDivs(src, topLimit, botLimit) =>
    fractalTop = f_fractalize(src) > 0 and src[2] >= topLimit ? src[2] : na
    fractalBot = f_fractalize(src) < 0 and src[2] <= botLimit ? src[2] : na
    highPrev = ta.valuewhen(fractalTop, src[2], 0)[2]
    highPrice = ta.valuewhen(fractalTop, high[2], 0)[2]
    lowPrev = ta.valuewhen(fractalBot, src[2], 0)[2]
    lowPrice = ta.valuewhen(fractalBot, low[2], 0)[2]
    bearSignal = fractalTop and high[1] > highPrice and src[1] < highPrev
    bullSignal = fractalBot and low[1] < lowPrice and src[1] > lowPrev
    [bearSignal, bullSignal]
    // Get user input
enableSR   = input(true, "SR On/Off", group="SR")
colorSup   = input(#00DBFF, "Support Color", group="SR")
colorRes   = input(#E91E63, "Resistance Color", group="SR")
strengthSR = input.int(2, "S/R Strength", 1, group="SR")
lineStyle  = input.string("Dotted", "Line Style", ["Solid", "Dotted", "Dashed"], group="SR")
lineWidth  = input.int(2, "S/R Line Width", 1, group="SR")
useZones   = input(true, "Zones On/Off", group="SR")
useHLZones = input(true, "High Low Zones On/Off", group="SR")
zoneWidth  = input.int(2, "Zone Width %", 0, tooltip="it's calculated using % of the distance between highest/lowest in last 300 bars", group="SR")
expandSR   = input(true, "Expand SR")
// Get components
rb            = 10
prd           = 284
ChannelW      = 10
label_loc     = 55
style         = lineStyle == "Solid" ? line.style_solid : lineStyle == "Dotted" ? line.style_dotted : line.style_dashed
ph            = ta.pivothigh(rb, rb)
pl            = ta.pivotlow (rb, rb)
sr_levels     = array.new_float(21, na)
prdhighest    = ta.highest(prd)
prdlowest     = ta.lowest(prd)
cwidth        = percWidth(prd, ChannelW)
zonePerc      = percWidth(300, zoneWidth)
aas           = array.new_bool(41, true)
u1            = 0.0, u1 := nz(u1[1])
d1            = 0.0, d1 := nz(d1[1])
highestph     = 0.0, highestph := highestph[1]
lowestpl      = 0.0, lowestpl := lowestpl[1]
var sr_levs   = array.new_float(21, na)
label hlabel  = na, label.delete(hlabel[1])
label llabel  = na, label.delete(llabel[1])
var sr_lines  = array.new_line(21, na)
var sr_linesH = array.new_line(21, na)
var sr_linesL = array.new_line(21, na)
var sr_linesF = array.new_linefill(21, na)
var sr_labels = array.new_label(21, na)
if ph or pl
    for x = 0 to array.size(sr_levels) - 1
        array.set(sr_levels, x, na)
    highestph := prdlowest
    lowestpl := prdhighest
    countpp = 0
    for x = 0 to prd
        if na(close[x])
            break
        if not na(ph[x]) or not na(pl[x])
            highestph := math.max(highestph, nz(ph[x], prdlowest), nz(pl[x], prdlowest))
            lowestpl := math.min(lowestpl, nz(ph[x], prdhighest), nz(pl[x], prdhighest))
            countpp += 1
            if countpp > 40
                break
            if array.get(aas, countpp)
                upl = (ph[x] ? high[x + rb] : low[x + rb]) + cwidth
                dnl = (ph[x] ? high[x + rb] : low[x + rb]) - cwidth
                u1 := countpp == 1 ? upl : u1
                d1 := countpp == 1 ? dnl : d1
                tmp = array.new_bool(41, true)
                cnt = 0
                tpoint = 0
                for xx = 0 to prd
                    if na(close[xx])
                        break
                    if not na(ph[xx]) or not na(pl[xx])
                        chg = false
                        cnt += 1
                        if cnt > 40
                            break
                        if array.get(aas, cnt)
                            if not na(ph[xx])
                                if high[xx + rb] <= upl and high[xx + rb] >= dnl
                                    tpoint += 1
                                    chg := true
                            if not na(pl[xx])
                                if low[xx + rb] <= upl and low[xx + rb] >= dnl
                                    tpoint += 1
                                    chg := true
                        if chg and cnt < 41
                            array.set(tmp, cnt, false)
                if tpoint >= strengthSR
                    for g = 0 to 40 by 1
                        if not array.get(tmp, g)
                            array.set(aas, g, false)
                    if ph[x] and countpp < 21
                        array.set(sr_levels, countpp, high[x + rb])
                    if pl[x] and countpp < 21
                        array.set(sr_levels, countpp, low[x + rb])
// Plot
var line highest_ = na, line.delete(highest_)
var line lowest_  = na, line.delete(lowest_)
var line highest_fill1 = na, line.delete(highest_fill1)
var line highest_fill2 = na, line.delete(highest_fill2)
var line lowest_fill1  = na, line.delete(lowest_fill1)
var line lowest_fill2  = na, line.delete(lowest_fill2)
hi_col = close >= highestph ? colorSup : colorRes
lo_col = close >= lowestpl  ? colorSup : colorRes
if enableSR
    highest_ := line.new(bar_index - 311, highestph, bar_index, highestph, xloc.bar_index, expandSR ? extend.both : extend.right, hi_col, style, lineWidth)
    lowest_  := line.new(bar_index - 311, lowestpl , bar_index, lowestpl , xloc.bar_index, expandSR ? extend.both : extend.right, lo_col, style, lineWidth)
    if useHLZones
        highest_fill1 := line.new(bar_index - 311, highestph + zonePerc, bar_index, highestph + zonePerc, xloc.bar_index, expandSR ? extend.both : extend.right, na)
        highest_fill2 := line.new(bar_index - 311, highestph - zonePerc, bar_index, highestph - zonePerc, xloc.bar_index, expandSR ? extend.both : extend.right, na)
        lowest_fill1  := line.new(bar_index - 311, lowestpl + zonePerc , bar_index, lowestpl + zonePerc , xloc.bar_index, expandSR ? extend.both : extend.right, na)
        lowest_fill2  := line.new(bar_index - 311, lowestpl - zonePerc , bar_index, lowestpl - zonePerc , xloc.bar_index, expandSR ? extend.both : extend.right, na)
        linefill.new(highest_fill1, highest_fill2, color.new(hi_col, 80))
        linefill.new(lowest_fill1 , lowest_fill2 , color.new(lo_col, 80))
if ph or pl
    for x = 0 to array.size(sr_lines) - 1
        array.set(sr_levs, x, array.get(sr_levels, x))
for x = 0 to array.size(sr_lines) - 1
    line.delete(array.get(sr_lines, x))
    line.delete(array.get(sr_linesH, x))
    line.delete(array.get(sr_linesL, x))
    linefill.delete(array.get(sr_linesF, x))
    if array.get(sr_levs, x) and enableSR
        line_col = close >= array.get(sr_levs, x) ? colorSup : colorRes
        array.set(sr_lines, x, line.new(bar_index - 355, array.get(sr_levs, x), bar_index, array.get(sr_levs, x), xloc.bar_index, expandSR ? extend.both : extend.right, line_col, style, lineWidth))
        if useZones
            array.set(sr_linesH, x, line.new(bar_index - 355, array.get(sr_levs, x) + zonePerc, bar_index, array.get(sr_levs, x) + zonePerc, xloc.bar_index, expandSR ? extend.both : extend.right, na))
            array.set(sr_linesL, x, line.new(bar_index - 355, array.get(sr_levs, x) - zonePerc, bar_index, array.get(sr_levs, x) - zonePerc, xloc.bar_index, expandSR ? extend.both : extend.right, na))
            array.set(sr_linesF, x, linefill.new(array.get(sr_linesH, x), array.get(sr_linesL, x), color.new(line_col, 80)))
for x = 0 to array.size(sr_labels) - 1
    label.delete(array.get(sr_labels, x))
    if array.get(sr_levs, x) and enableSR
        lab_loc = close >= array.get(sr_levs, x) ? label.style_label_up : label.style_label_down
        lab_col = close >= array.get(sr_levs, x) ? colorSup             : colorRes
        array.set(sr_labels, x, label.new(bar_index + label_loc, array.get(sr_levs, x), str.tostring(math.round_to_mintick(array.get(sr_levs, x))), color=lab_col , textcolor=#000000, style=lab_loc))
hlabel := enableSR ? label.new(bar_index + label_loc + math.round(math.sign(label_loc)) * 20, highestph, "High Level : " + str.tostring(highestph), color=hi_col, textcolor=#000000, style=label.style_label_down) : na
llabel := enableSR ? label.new(bar_index + label_loc + math.round(math.sign(label_loc)) * 20, lowestpl , "Low  Level : " + str.tostring(lowestpl) , color=lo_col, textcolor=#000000, style=label.style_label_up  ) : na


// Get components
rsi       = ta.rsi(close, 28)
//rsiOb     = rsi > 78 and rsi > ta.ema(rsi, 10)
//rsiOs     = rsi < 27 and rsi < ta.ema(rsi, 10)
rsiOb     = rsi > 65 and rsi > ta.ema(rsi, 10)
rsiOs     = rsi < 35 and rsi < ta.ema(rsi, 10)
dHigh     = securityNoRep(syminfo.tickerid, "D", high [1])
dLow      = securityNoRep(syminfo.tickerid, "D", low  [1])
dClose    = securityNoRep(syminfo.tickerid, "D", close[1])
ema = ta.ema(close, 144)
emaBull = close > ema
equal_tf(res) =>
    hasS = str.contains(res, "S")
    numericRes = hasS ? str.tonumber(str.replace(res, "S", "")) / 60.0 : str.tonumber(res)
    numericRes == f_chartTfInMinutes()

higher_tf(res) =>
    hasS = str.contains(res, "S")
    numericRes = hasS ? str.tonumber(str.replace(res, "S", "")) / 60.0 : str.tonumber(res)
    numericRes > f_chartTfInMinutes()
too_small_tf(res) => (timeframe.isweekly and res=="1") or (timeframe.ismonthly and str.tonumber(res) < 10)
securityNoRep1(sym, res, src) =>
    bool bull_ = na
    hasS = str.contains(res, "S")
    actualRes = hasS ? res : res

    bull_ := equal_tf(actualRes) ? src : bull_
    bull_ := higher_tf(actualRes) ? request.security(sym, actualRes, src, barmerge.gaps_off, barmerge.lookahead_on) : bull_

    // For lower timeframes handling
    lowerTfToUse = higher_tf(actualRes) ?
        (timeframe.isseconds ? str.tostring(f_chartTfInMinutes() * 60) + "S" : str.tostring(f_chartTfInMinutes())) :
        too_small_tf(actualRes) ? (timeframe.isweekly ? "3" : "10") : actualRes

    bull_array = request.security_lower_tf(syminfo.tickerid, lowerTfToUse, src)

    if array.size(bull_array) > 1 and not equal_tf(actualRes) and not higher_tf(actualRes)
        bull_ := array.pop(bull_array)
    array.clear(bull_array)
    bull_
// Lower timeframe support
TFLowerBull = lowerTfEnabled ? securityNoRep1(syminfo.tickerid, lowerTfValue + "S", emaBull) : na
TF1Bull   = securityNoRep1(syminfo.tickerid, "1"   , emaBull)
TF3Bull   = securityNoRep1(syminfo.tickerid, "3"   , emaBull)
TF5Bull   = securityNoRep1(syminfo.tickerid, "5"   , emaBull)
TF15Bull  = securityNoRep1(syminfo.tickerid, "15"  , emaBull)
TF30Bull  = securityNoRep1(syminfo.tickerid, "30"  , emaBull)
TF60Bull  = securityNoRep1(syminfo.tickerid, "60"  , emaBull)
TF120Bull = securityNoRep1(syminfo.tickerid, "120" , emaBull)
TF240Bull = securityNoRep1(syminfo.tickerid, "240" , emaBull)
TF480Bull = securityNoRep1(syminfo.tickerid, "480" , emaBull)
TFDBull   = securityNoRep1(syminfo.tickerid, "1440", emaBull)
[wt1, wt2] = wavetrend(close, 5, 10)
[wtDivBear1, wtDivBull1] = f_findDivs(wt2, 15, -40)
[wtDivBear2, wtDivBull2] = f_findDivs(wt2, 45, -65)
wtDivBull = wtDivBull1 or wtDivBull2
wtDivBear = wtDivBear1 or wtDivBear2
////////////////////////////////////////////////////////
// === BASE FUNCTIONS ===
// Returns MA input selection variant, default to SMA if blank or typo.
variant(type, src, len, offSig, offALMA) =>
    v1 = ta.sma(src, len)  // Simple
    v2 = ta.ema(src, len)  // Exponential
    v3 = 2 * v2 - ta.ema(v2, len)  // Double Exponential
    v4 = 3 * (v2 - ta.ema(v2, len)) + ta.ema(ta.ema(v2, len), len)  // Triple Exponential
    v5 = ta.wma(src, len)  // Weighted
    v6 = ta.vwma(src, len)  // Volume Weighted
    v7 = 0.0
    sma_1 = ta.sma(src, len)  // Smoothed
    v7 := na(v7[1]) ? sma_1 : (v7[1] * (len - 1) + src) / len
    v8 = ta.wma(2 * ta.wma(src, len / 2) - ta.wma(src, len), math.round(math.sqrt(len)))  // Hull
    v9 = ta.linreg(src, len, offSig)  // Least Squares
    v10 = ta.alma(src, len, offALMA, offSig)  // Arnaud Legoux
    v11 = ta.sma(v1, len)  // Triangular (extreme smooth)
    // SuperSmoother filter
    // 2013  John F. Ehlers
    a1 = math.exp(-1.414 * 3.14159 / len)
    b1 = 2 * a1 * math.cos(1.414 * 3.14159 / len)
    c2 = b1
    c3 = -a1 * a1
    c1 = 1 - c2 - c3
    v12 = 0.0
    v12 := c1 * (src + nz(src[1])) / 2 + c2 * nz(v12[1]) + c3 * nz(v12[2])
    type == 'EMA' ? v2 : type == 'DEMA' ? v3 : type == 'TEMA' ? v4 : type == 'WMA' ? v5 : type == 'VWMA' ? v6 : type == 'SMMA' ? v7 : type == 'HullMA' ? v8 : type == 'LSMA' ? v9 : type == 'ALMA' ? v10 : type == 'TMA' ? v11 : type == 'SSMA' ? v12 : v1

// security wrapper for repeat calls
reso(exp, use, res) =>
    hasS = str.contains(res, "S")
    actualRes = hasS ? str.replace(res, "S", "") + "" : res
    // Use the appropriate security function based on the timeframe
    security_1 = request.security(syminfo.tickerid, actualRes, exp, gaps=barmerge.gaps_off, lookahead=barmerge.lookahead_on)
    use ? security_1 : exp

// === /BASE FUNCTIONS ===
// === SERIES SETUP ===
closeSeries = variant(basisType, close[delayOffset], basisLen, offsetSigma, offsetALMA)
openSeries = variant(basisType, open[delayOffset], basisLen, offsetSigma, offsetALMA)
// === /SERIES ===

// Get Alternate resolution Series if selected.
closeSeriesAlt = reso(closeSeries, useRes, stratRes)
openSeriesAlt = reso(openSeries, useRes, stratRes)
//
// === ALERT conditions
xlong = ta.crossover(closeSeriesAlt, openSeriesAlt)
xshort = ta.crossunder(closeSeriesAlt, openSeriesAlt)
longCond = xlong  // alternative: longCond[1]? false : (xlong or xlong[1]) and close>closeSeriesAlt and close>=open
shortCond = xshort  // alternative: shortCond[1]? false : (xshort or xshort[1]) and close<closeSeriesAlt and close<=open
// === /ALERT conditions.
buy = ta.crossover(closeSeriesAlt, openSeriesAlt)
sell = ta.crossunder(closeSeriesAlt, openSeriesAlt)

plotshape(buy,  title = "Buy",  text = 'Buy',  style = shape.labelup,   location = location.belowbar, color= #00DBFF, textcolor = #FFFFFF, transp = 0, size = size.tiny)
plotshape(sell, title = "Sell", text = 'Sell', style = shape.labeldown, location = location.abovebar, color= #E91E63, textcolor = #FFFFFF, transp = 0, size = size.tiny)

// Create separate buy/sell conditions for lower timeframes
buyLower = lowerTfEnabled ? TFLowerBull and not TFLowerBull[1] : na
sellLower = lowerTfEnabled ? not TFLowerBull and TFLowerBull[1] : na

// Plot lower timeframe alerts with different shape
plotshape(buyLower,  title = "Buy Lower",  text = 'Buy ' + lowerTfValue + 's',  style = shape.triangleup,   location = location.belowbar, color= color.new(#00DBFF, 20), textcolor = #FFFFFF, transp = 0, size = size.tiny)
plotshape(sellLower, title = "Sell Lower", text = 'Sell ' + lowerTfValue + 's', style = shape.triangledown, location = location.abovebar, color= color.new(#E91E63, 20), textcolor = #FFFFFF, transp = 0, size = size.tiny)

//
// === ENHANCED LOWER TIMEFRAME VISUAL INDICATORS ===
//

// Enhanced Lower Timeframe RSI Signals
plotshape(lowerTfEnabled and showLowerTfRSI and lowerTfRSIOverbought, title="LTF RSI OB", text="RSI OB", style=shape.diamond, location=location.abovebar, color=color.new(color.red, 30), textcolor=color.white, size=size.tiny)
plotshape(lowerTfEnabled and showLowerTfRSI and lowerTfRSIOversold, title="LTF RSI OS", text="RSI OS", style=shape.diamond, location=location.belowbar, color=color.new(color.green, 30), textcolor=color.white, size=size.tiny)
plotshape(lowerTfEnabled and showLowerTfRSI and lowerTfRSIBullCross, title="LTF RSI Bull", text="RSI↑", style=shape.circle, location=location.belowbar, color=color.new(color.lime, 20), textcolor=color.white, size=size.tiny)
plotshape(lowerTfEnabled and showLowerTfRSI and lowerTfRSIBearCross, title="LTF RSI Bear", text="RSI↓", style=shape.circle, location=location.abovebar, color=color.new(color.orange, 20), textcolor=color.white, size=size.tiny)

// Enhanced Lower Timeframe MACD Signals
plotshape(lowerTfEnabled and showLowerTfMACD and lowerTfMACDBullCross, title="LTF MACD Bull", text="MACD↑", style=shape.square, location=location.belowbar, color=color.new(color.aqua, 20), textcolor=color.white, size=size.tiny)
plotshape(lowerTfEnabled and showLowerTfMACD and lowerTfMACDBearCross, title="LTF MACD Bear", text="MACD↓", style=shape.square, location=location.abovebar, color=color.new(color.purple, 20), textcolor=color.white, size=size.tiny)

// Enhanced Lower Timeframe Bollinger Bands Signals
plotshape(lowerTfEnabled and showLowerTfBB and lowerTfBBBullSignal, title="LTF BB Bull", text="BB↑", style=shape.flag, location=location.belowbar, color=color.new(color.blue, 30), textcolor=color.white, size=size.tiny)
plotshape(lowerTfEnabled and showLowerTfBB and lowerTfBBBearSignal, title="LTF BB Bear", text="BB↓", style=shape.flag, location=location.abovebar, color=color.new(color.maroon, 30), textcolor=color.white, size=size.tiny)
plotshape(lowerTfEnabled and showLowerTfBB and lowerTfBBSqueeze, title="LTF BB Squeeze", text="SQZ", style=shape.xcross, location=location.absolute, color=color.new(color.yellow, 20), textcolor=color.black, size=size.tiny)

// Enhanced Lower Timeframe Supertrend Signals
plotshape(lowerTfEnabled and showLowerTfSupertrend and lowerTfSTBullChange, title="LTF ST Bull", text="ST↑", style=shape.arrowup, location=location.belowbar, color=color.new(color.green, 10), textcolor=color.white, size=size.small)
plotshape(lowerTfEnabled and showLowerTfSupertrend and lowerTfSTBearChange, title="LTF ST Bear", text="ST↓", style=shape.arrowdown, location=location.abovebar, color=color.new(color.red, 10), textcolor=color.white, size=size.small)

// Enhanced Lower Timeframe Volume Signals
plotshape(lowerTfEnabled and showLowerTfVolume and lowerTfVolumeSpike, title="LTF Volume Spike", text="VOL", style=shape.cross, location=location.absolute, color=color.new(color.fuchsia, 30), textcolor=color.white, size=size.tiny)

// Enhanced Lower Timeframe Divergence Signals
plotshape(lowerTfEnabled and showLowerTfDivergence and lowerTfBullDiv, title="LTF Bull Div", text="DIV↑", style=shape.labelup, location=location.belowbar, color=color.new(color.lime, 0), textcolor=color.black, size=size.small)
plotshape(lowerTfEnabled and showLowerTfDivergence and lowerTfBearDiv, title="LTF Bear Div", text="DIV↓", style=shape.labeldown, location=location.abovebar, color=color.new(color.red, 0), textcolor=color.white, size=size.small)

// Enhanced Lower Timeframe Breakout Signals
plotshape(lowerTfEnabled and showLowerTfBreakouts and lowerTfBullBreakout, title="LTF Bull Breakout", text="BRK↑", style=shape.triangleup, location=location.belowbar, color=color.new(color.green, 0), textcolor=color.white, size=size.normal)
plotshape(lowerTfEnabled and showLowerTfBreakouts and lowerTfBearBreakout, title="LTF Bear Breakout", text="BRK↓", style=shape.triangledown, location=location.abovebar, color=color.new(color.red, 0), textcolor=color.white, size=size.normal)

// Plot Lower Timeframe Supertrend Line (if enabled)
plot(lowerTfEnabled and showLowerTfSupertrend and showLowerTfIndicators ? lowerTfST : na, title="LTF Supertrend", color=lowerTfSTBull ? color.new(color.green, 50) : color.new(color.red, 50), linewidth=1, style=plot.style_line)

// Plot Lower Timeframe Bollinger Bands (if enabled)
plot(lowerTfEnabled and showLowerTfBB and showLowerTfIndicators ? lowerTfBBUpper : na, title="LTF BB Upper", color=color.new(color.blue, 70), linewidth=1, style=plot.style_line)
plot(lowerTfEnabled and showLowerTfBB and showLowerTfIndicators ? lowerTfBBLower : na, title="LTF BB Lower", color=color.new(color.blue, 70), linewidth=1, style=plot.style_line)
plot(lowerTfEnabled and showLowerTfBB and showLowerTfIndicators ? lowerTfBBMiddle : na, title="LTF BB Middle", color=color.new(color.gray, 80), linewidth=1, style=plot.style_line)

//
// === END ENHANCED LOWER TIMEFRAME VISUAL INDICATORS ===
//

// === STRATEGY ===
// stop loss
slPoints = input.int(defval=0, title='Initial Stop Loss Points (zero to disable)', minval=0)
tpPoints = input.int(defval=0, title='Initial Target Profit Points (zero for disable)', minval=0)
// Include bar limiting algorithm
ebar = input.int(defval=4000, title='Number of Bars for Back Testing', minval=0)
dummy = input(false, title='- SET to ZERO for Daily or Longer Timeframes')
//
// Calculate how many mars since last bar
tdays = (timenow - time) / 60000.0  // number of minutes since last bar
tdays := timeframe.ismonthly ? tdays / 1440.0 / 5.0 / 4.3 / timeframe.multiplier : timeframe.isweekly ? tdays / 1440.0 / 5.0 / timeframe.multiplier : timeframe.isdaily ? tdays / 1440.0 / timeframe.multiplier : tdays / timeframe.multiplier  // number of bars since last bar
//
//set up exit parameters
TP = tpPoints > 0 ? tpPoints : na
SL = slPoints > 0 ? slPoints : na

// === /STRATEGY ===
////////////////////////////////////////////////////////////////////////////////
// to automate put this in trendinview message:     {{strategy.order.alert_message}}
i_alert_txt_entry_long = input.text_area(defval = "", title = "Long Entry Message", group = "Alerts")
i_alert_txt_entry_short = input.text_area(defval = "", title = "Short Entry Message", group = "Alerts")

// Entries and Exits with TP/SL
// Define alert messages for lower timeframe signals
i_alert_txt_entry_long_lower = i_alert_txt_entry_long + " (" + lowerTfValue + "s)"
i_alert_txt_entry_short_lower = i_alert_txt_entry_short + " (" + lowerTfValue + "s)"

if buy
    //strategy.close("Short" , alert_message = i_alert_txt_exit_short)
    strategy.entry("Long" , strategy.long , alert_message = i_alert_txt_entry_long)

if sell
    //strategy.close("Long" , alert_message = i_alert_txt_exit_long)
    strategy.entry("Short" , strategy.short, alert_message = i_alert_txt_entry_short)

// Handle lower timeframe entries if enabled
if lowerTfEnabled
    if buyLower
        strategy.entry("Long_" + lowerTfValue + "s", strategy.long, alert_message = i_alert_txt_entry_long_lower)

    if sellLower
        strategy.entry("Short_" + lowerTfValue + "s", strategy.short, alert_message = i_alert_txt_entry_short_lower)

//
// === ENHANCED LOWER TIMEFRAME STRATEGY ENTRIES ===
//

// Enhanced Lower Timeframe Alert Messages
i_alert_txt_rsi_bull = "Lower TF RSI Bullish Signal (" + lowerTfValue + "s)"
i_alert_txt_rsi_bear = "Lower TF RSI Bearish Signal (" + lowerTfValue + "s)"
i_alert_txt_macd_bull = "Lower TF MACD Bullish Signal (" + lowerTfValue + "s)"
i_alert_txt_macd_bear = "Lower TF MACD Bearish Signal (" + lowerTfValue + "s)"
i_alert_txt_bb_bull = "Lower TF BB Bullish Signal (" + lowerTfValue + "s)"
i_alert_txt_bb_bear = "Lower TF BB Bearish Signal (" + lowerTfValue + "s)"
i_alert_txt_st_bull = "Lower TF Supertrend Bullish Signal (" + lowerTfValue + "s)"
i_alert_txt_st_bear = "Lower TF Supertrend Bearish Signal (" + lowerTfValue + "s)"
i_alert_txt_vol_spike = "Lower TF Volume Spike (" + lowerTfValue + "s)"
i_alert_txt_div_bull = "Lower TF Bullish Divergence (" + lowerTfValue + "s)"
i_alert_txt_div_bear = "Lower TF Bearish Divergence (" + lowerTfValue + "s)"
i_alert_txt_brk_bull = "Lower TF Bullish Breakout (" + lowerTfValue + "s)"
i_alert_txt_brk_bear = "Lower TF Bearish Breakout (" + lowerTfValue + "s)"

// Composite Lower Timeframe Signals
lowerTfBullComposite = lowerTfEnabled and (
    (showLowerTfRSI and (lowerTfRSIBullCross or lowerTfRSIOversold)) or
    (showLowerTfMACD and lowerTfMACDBullCross) or
    (showLowerTfSupertrend and lowerTfSTBullChange) or
    (showLowerTfDivergence and lowerTfBullDiv) or
    (showLowerTfBreakouts and lowerTfBullBreakout)
)

lowerTfBearComposite = lowerTfEnabled and (
    (showLowerTfRSI and (lowerTfRSIBearCross or lowerTfRSIOverbought)) or
    (showLowerTfMACD and lowerTfMACDBearCross) or
    (showLowerTfSupertrend and lowerTfSTBearChange) or
    (showLowerTfDivergence and lowerTfBearDiv) or
    (showLowerTfBreakouts and lowerTfBearBreakout)
)

// Enhanced Strategy Entries for Lower Timeframe Signals
if lowerTfEnabled and showLowerTfRSI
    if lowerTfRSIBullCross
        strategy.entry("LTF_RSI_Long", strategy.long, alert_message = i_alert_txt_rsi_bull)
    if lowerTfRSIBearCross
        strategy.entry("LTF_RSI_Short", strategy.short, alert_message = i_alert_txt_rsi_bear)

if lowerTfEnabled and showLowerTfMACD
    if lowerTfMACDBullCross
        strategy.entry("LTF_MACD_Long", strategy.long, alert_message = i_alert_txt_macd_bull)
    if lowerTfMACDBearCross
        strategy.entry("LTF_MACD_Short", strategy.short, alert_message = i_alert_txt_macd_bear)

if lowerTfEnabled and showLowerTfSupertrend
    if lowerTfSTBullChange
        strategy.entry("LTF_ST_Long", strategy.long, alert_message = i_alert_txt_st_bull)
    if lowerTfSTBearChange
        strategy.entry("LTF_ST_Short", strategy.short, alert_message = i_alert_txt_st_bear)

if lowerTfEnabled and showLowerTfDivergence
    if lowerTfBullDiv
        strategy.entry("LTF_DIV_Long", strategy.long, alert_message = i_alert_txt_div_bull)
    if lowerTfBearDiv
        strategy.entry("LTF_DIV_Short", strategy.short, alert_message = i_alert_txt_div_bear)

if lowerTfEnabled and showLowerTfBreakouts
    if lowerTfBullBreakout
        strategy.entry("LTF_BRK_Long", strategy.long, alert_message = i_alert_txt_brk_bull)
    if lowerTfBearBreakout
        strategy.entry("LTF_BRK_Short", strategy.short, alert_message = i_alert_txt_brk_bear)

// Composite Signal Entries
if lowerTfBullComposite
    strategy.entry("LTF_COMP_Long", strategy.long, alert_message = "Lower TF Composite Bullish Signal (" + lowerTfValue + "s)")
if lowerTfBearComposite
    strategy.entry("LTF_COMP_Short", strategy.short, alert_message = "Lower TF Composite Bearish Signal (" + lowerTfValue + "s)")

//
// === END ENHANCED LOWER TIMEFRAME STRATEGY ENTRIES ===
//

// === ENHANCED ALERTS ===
alertcondition(lowerTfEnabled and lowerTfRSIBullCross, title="LTF RSI Bull Cross", message="Lower TF RSI Bullish Crossover")
alertcondition(lowerTfEnabled and lowerTfRSIBearCross, title="LTF RSI Bear Cross", message="Lower TF RSI Bearish Crossover")
alertcondition(lowerTfEnabled and lowerTfMACDBullCross, title="LTF MACD Bull Cross", message="Lower TF MACD Bullish Crossover")
alertcondition(lowerTfEnabled and lowerTfMACDBearCross, title="LTF MACD Bear Cross", message="Lower TF MACD Bearish Crossover")
alertcondition(lowerTfEnabled and lowerTfSTBullChange, title="LTF Supertrend Bull", message="Lower TF Supertrend Bullish Change")
alertcondition(lowerTfEnabled and lowerTfSTBearChange, title="LTF Supertrend Bear", message="Lower TF Supertrend Bearish Change")
alertcondition(lowerTfEnabled and lowerTfVolumeSpike, title="LTF Volume Spike", message="Lower TF Volume Spike Detected")
alertcondition(lowerTfEnabled and lowerTfBullDiv, title="LTF Bull Divergence", message="Lower TF Bullish Divergence Detected")
alertcondition(lowerTfEnabled and lowerTfBearDiv, title="LTF Bear Divergence", message="Lower TF Bearish Divergence Detected")
alertcondition(lowerTfEnabled and lowerTfBullBreakout, title="LTF Bull Breakout", message="Lower TF Bullish Breakout Detected")
alertcondition(lowerTfEnabled and lowerTfBearBreakout, title="LTF Bear Breakout", message="Lower TF Bearish Breakout Detected")
alertcondition(lowerTfBullComposite, title="LTF Composite Bull", message="Lower TF Composite Bullish Signal")
alertcondition(lowerTfBearComposite, title="LTF Composite Bear", message="Lower TF Composite Bearish Signal")

//
// === LOWER TIMEFRAME STATUS TABLE ===
//

// Input for showing status table
showLowerTfTable = input.bool(defval=true, title="Show Lower TF Status Table", group="LOWER TF DISPLAY")
tablePosition = input.string(defval="top_right", title="Table Position", options=["top_left", "top_center", "top_right", "middle_left", "middle_center", "middle_right", "bottom_left", "bottom_center", "bottom_right"], group="LOWER TF DISPLAY")

// Function to get table position
f_getTablePosition(pos) =>
    switch pos
        "top_left" => position.top_left
        "top_center" => position.top_center
        "top_right" => position.top_right
        "middle_left" => position.middle_left
        "middle_center" => position.middle_center
        "middle_right" => position.middle_right
        "bottom_left" => position.bottom_left
        "bottom_center" => position.bottom_center
        "bottom_right" => position.bottom_right
        => position.top_right

// Create and populate the status table
if lowerTfEnabled and showLowerTfTable and barstate.islast
    var table statusTable = table.new(f_getTablePosition(tablePosition), 3, 10, bgcolor=color.new(color.black, 80), border_width=1, border_color=color.gray)

    // Table headers
    table.cell(statusTable, 0, 0, "Lower TF (" + lowerTfValue + "s)", text_color=color.white, bgcolor=color.new(color.blue, 70), text_size=size.small)
    table.cell(statusTable, 1, 0, "Status", text_color=color.white, bgcolor=color.new(color.blue, 70), text_size=size.small)
    table.cell(statusTable, 2, 0, "Signal", text_color=color.white, bgcolor=color.new(color.blue, 70), text_size=size.small)

    // RSI Status
    if showLowerTfRSI
        rsiStatus = lowerTfRSIOverbought ? "OB" : lowerTfRSIOversold ? "OS" : "Normal"
        rsiColor = lowerTfRSIOverbought ? color.red : lowerTfRSIOversold ? color.green : color.gray
        rsiSignal = lowerTfRSIBullCross ? "↑" : lowerTfRSIBearCross ? "↓" : "-"
        table.cell(statusTable, 0, 1, "RSI", text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)
        table.cell(statusTable, 1, 1, rsiStatus, text_color=color.white, bgcolor=color.new(rsiColor, 70), text_size=size.tiny)
        table.cell(statusTable, 2, 1, rsiSignal, text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)

    // MACD Status
    if showLowerTfMACD
        macdStatus = lowerTfMACD > lowerTfMACDSignal ? "Bull" : "Bear"
        macdColor = lowerTfMACD > lowerTfMACDSignal ? color.green : color.red
        macdSignal = lowerTfMACDBullCross ? "↑" : lowerTfMACDBearCross ? "↓" : "-"
        table.cell(statusTable, 0, 2, "MACD", text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)
        table.cell(statusTable, 1, 2, macdStatus, text_color=color.white, bgcolor=color.new(macdColor, 70), text_size=size.tiny)
        table.cell(statusTable, 2, 2, macdSignal, text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)

    // Bollinger Bands Status
    if showLowerTfBB
        bbStatus = lowerTfBBBullSignal ? "Above" : lowerTfBBBearSignal ? "Below" : "Inside"
        bbColor = lowerTfBBBullSignal ? color.blue : lowerTfBBBearSignal ? color.maroon : color.gray
        bbSignal = lowerTfBBSqueeze ? "SQZ" : "-"
        table.cell(statusTable, 0, 3, "BB", text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)
        table.cell(statusTable, 1, 3, bbStatus, text_color=color.white, bgcolor=color.new(bbColor, 70), text_size=size.tiny)
        table.cell(statusTable, 2, 3, bbSignal, text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)

    // Supertrend Status
    if showLowerTfSupertrend
        stStatus = lowerTfSTBull ? "Bull" : "Bear"
        stColor = lowerTfSTBull ? color.green : color.red
        stSignal = lowerTfSTBullChange ? "↑" : lowerTfSTBearChange ? "↓" : "-"
        table.cell(statusTable, 0, 4, "ST", text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)
        table.cell(statusTable, 1, 4, stStatus, text_color=color.white, bgcolor=color.new(stColor, 70), text_size=size.tiny)
        table.cell(statusTable, 2, 4, stSignal, text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)

    // Volume Status
    if showLowerTfVolume
        volStatus = lowerTfVolumeSpike ? "Spike" : "Normal"
        volColor = lowerTfVolumeSpike ? color.fuchsia : color.gray
        table.cell(statusTable, 0, 5, "Volume", text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)
        table.cell(statusTable, 1, 5, volStatus, text_color=color.white, bgcolor=color.new(volColor, 70), text_size=size.tiny)
        table.cell(statusTable, 2, 5, "-", text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)

    // Divergence Status
    if showLowerTfDivergence
        divStatus = lowerTfBullDiv ? "Bull Div" : lowerTfBearDiv ? "Bear Div" : "None"
        divColor = lowerTfBullDiv ? color.lime : lowerTfBearDiv ? color.red : color.gray
        divSignal = lowerTfBullDiv ? "↑" : lowerTfBearDiv ? "↓" : "-"
        table.cell(statusTable, 0, 6, "Divergence", text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)
        table.cell(statusTable, 1, 6, divStatus, text_color=color.white, bgcolor=color.new(divColor, 70), text_size=size.tiny)
        table.cell(statusTable, 2, 6, divSignal, text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)

    // Breakout Status
    if showLowerTfBreakouts
        brkStatus = lowerTfBullBreakout ? "Bull BRK" : lowerTfBearBreakout ? "Bear BRK" : "None"
        brkColor = lowerTfBullBreakout ? color.green : lowerTfBearBreakout ? color.red : color.gray
        brkSignal = lowerTfBullBreakout ? "↑" : lowerTfBearBreakout ? "↓" : "-"
        table.cell(statusTable, 0, 7, "Breakout", text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)
        table.cell(statusTable, 1, 7, brkStatus, text_color=color.white, bgcolor=color.new(brkColor, 70), text_size=size.tiny)
        table.cell(statusTable, 2, 7, brkSignal, text_color=color.white, bgcolor=color.new(color.black, 50), text_size=size.tiny)

    // Composite Signal Status
    compStatus = lowerTfBullComposite ? "BULL" : lowerTfBearComposite ? "BEAR" : "NEUTRAL"
    compColor = lowerTfBullComposite ? color.green : lowerTfBearComposite ? color.red : color.gray
    compSignal = lowerTfBullComposite ? "↑" : lowerTfBearComposite ? "↓" : "-"
    table.cell(statusTable, 0, 8, "COMPOSITE", text_color=color.white, bgcolor=color.new(color.yellow, 70), text_size=size.tiny)
    table.cell(statusTable, 1, 8, compStatus, text_color=color.white, bgcolor=color.new(compColor, 50), text_size=size.tiny)
    table.cell(statusTable, 2, 8, compSignal, text_color=color.white, bgcolor=color.new(color.yellow, 70), text_size=size.tiny)

//
// === END LOWER TIMEFRAME STATUS TABLE ===
//